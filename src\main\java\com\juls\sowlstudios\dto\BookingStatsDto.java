package com.juls.sowlstudios.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Map;

@Data
@AllArgsConstructor
@Builder
public class BookingStatsDto {
    private Long totalBookings;
    private Long todayBookings;
    private <PERSON> weeklyBookings;
    private Long monthlyBookings;
    private Long pendingBookings;
    private Long confirmedBookings;
    private Long cancelledBookings;
    private Long completedBookings;
    private Map<String, Long> packageStats;
}