package com.juls.sowlstudios.controller;


import com.juls.sowlstudios.dto.BookingStatsDto;
import com.juls.sowlstudios.dto.response.ApiResponse;
import com.juls.sowlstudios.dto.response.BookingResponseDto;
import com.juls.sowlstudios.entity.Booking;
import com.juls.sowlstudios.service.BookingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/admin")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
@Slf4j
public class AdminController {

    private final BookingService bookingService;

    @GetMapping("/dashboard/stats")
    public ResponseEntity<ApiResponse<BookingStatsDto>> getDashboardStats() {
        BookingStatsDto stats = bookingService.getBookingStatistics();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    @GetMapping("/bookings")
    public ResponseEntity<ApiResponse<Page<BookingResponseDto>>> getAllBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Booking.BookingStatus status) {

        Page<BookingResponseDto> bookings = bookingService.getAllBookings(page, size, status);
        return ResponseEntity.ok(ApiResponse.success(bookings));
    }

    @GetMapping("/bookings/{id}")
    public ResponseEntity<ApiResponse<BookingResponseDto>> getBookingById(@PathVariable Long id) {
        try {
            BookingResponseDto booking = bookingService.getBookingById(id);
            return ResponseEntity.ok(ApiResponse.success(booking));
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/bookings/{id}/status")
    public ResponseEntity<ApiResponse<BookingResponseDto>> updateBookingStatus(
            @PathVariable Long id,
            @RequestParam Booking.BookingStatus status) {

        try {
            BookingResponseDto booking = bookingService.updateBookingStatus(id, status);
            log.info("Booking {} status updated to {} by admin", id, status);
            return ResponseEntity.ok(ApiResponse.success("Status updated successfully", booking));
        } catch (Exception e) {
            log.error("Error updating booking status", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update status: " + e.getMessage()));
        }
    }

    @DeleteMapping("/bookings/{id}")
    public ResponseEntity<ApiResponse<String>> deleteBooking(@PathVariable Long id) {
        try {
            bookingService.deleteBooking(id);
            log.info("Booking {} deleted by admin", id);
            return ResponseEntity.ok(ApiResponse.success("Booking deleted successfully", null));
        } catch (Exception e) {
            log.error("Error deleting booking", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete booking: " + e.getMessage()));
        }
    }
}
