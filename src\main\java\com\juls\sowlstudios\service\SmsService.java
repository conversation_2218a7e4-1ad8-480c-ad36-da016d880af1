package com.juls.sowlstudios.service;

import com.juls.sowlstudios.entity.Booking;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static com.juls.sowlstudios.entity.Booking.BookingStatus.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class SmsService {

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${mnotify.api.key}")
    private String apiKey;

    @Value("${mnotify.api.url}")
    private String apiUrl;

    @Value("${mnotify.sender.id}")
    private String senderId;

    @Value("${mnotify.enabled:true}")
    private boolean smsEnabled;

    @Async("taskExecutor")
    public void sendBookingConfirmation(Booking booking) {
        if (!smsEnabled) {
            log.info("SMS service is disabled, skipping SMS for booking ID: {}", booking.getId());
            return;
        }

        try {
            String message = buildConfirmationMessage(booking);
            sendSms(booking.getPhoneNumber(), message);
            log.info("SMS confirmation sent for booking ID: {}", booking.getId());
        } catch (Exception e) {
            log.error("Failed to send SMS for booking ID: {}", booking.getId(), e);
        }
    }

    @Async("taskExecutor")
    public void sendStatusUpdate(Booking booking) {
        if (!smsEnabled) {
            return;
        }

        try {
            String message = buildStatusUpdateMessage(booking);
            sendSms(booking.getPhoneNumber(), message);
            log.info("SMS status update sent for booking ID: {}", booking.getId());
        } catch (Exception e) {
            log.error("Failed to send status update SMS for booking ID: {}", booking.getId(), e);
        }
    }

    private void sendSms(String phoneNumber, String message) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("recipient", phoneNumber);
        requestBody.put("sender", senderId);
        requestBody.put("message", message);
        requestBody.put("is_schedule", false);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, request, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("SMS sent successfully to: {}", phoneNumber);
            } else {
                log.error("Failed to send SMS. Status: {}, Response: {}",
                        response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.error("Error sending SMS to: {}", phoneNumber, e);
            throw e;
        }
    }

    private String buildConfirmationMessage(Booking booking) {
        return String.format("""
            Hi %s! Your photography session booking (ID: %d) has been received. 
            Package: %s. We'll contact you soon with session details. 
            Thank you for choosing us!
            """,
                booking.getFirstName(),
                booking.getId(),
                booking.getPackagePreference());
    }

    private String buildStatusUpdateMessage(Booking booking) {
        String statusMessage = switch (booking.getStatus()) {
            case CONFIRMED -> "confirmed! We'll contact you with session details.";
            case CANCELLED -> "cancelled. Please contact us if you have questions.";
            case COMPLETED -> "completed! Thank you for choosing our services.";
            default -> "updated to " + booking.getStatus().toString().toLowerCase() + ".";
        };

        return String.format("""
            Hi %s! Your photography booking (ID: %d) has been %s
            """,
                booking.getFirstName(),
                booking.getId(),
                statusMessage);
    }
}