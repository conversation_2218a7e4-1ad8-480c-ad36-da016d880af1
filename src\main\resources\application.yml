spring:
  application:
    name: photography-booking-api

  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  h2:
    console:
      enabled: true
      path: /h2-console

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

  cache:
    type: simple

server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /

logging:
  level:
    com.photography.booking: ${LOG_LEVEL:INFO}
    org.springframework.security: WARN
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: WARN

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Custom application properties
app:
  cors:
    allowed-origins: ${FRONTEND_URL:http://localhost:3000},${FRONTEND_URL_PROD:https://your-frontend-domain.com}

  jwt:
    secret: ${JWT_SECRET:photography-booking-secret-key-that-should-be-at-least-256-bits-long}
    expiration: ${JWT_EXPIRATION:86400000} # 24 hours

  admin:
    default:
      username: ${ADMIN_USERNAME:admin}
      password: ${ADMIN_PASSWORD:admin123}
      email: ${ADMIN_EMAIL:<EMAIL>}
      fullName: ${ADMIN_FULL_NAME:System Administrator}

  email:
    from: ${SMTP_USERNAME:<EMAIL>}
    admin: ${ADMIN_EMAIL:<EMAIL>}
    enabled: ${EMAIL_ENABLED:false}

# MNotify SMS Configuration
mnotify:
  api:
    key: ${MNOTIFY_API_KEY:your-mnotify-api-key}
    url: ${MNOTIFY_API_URL:https://api.mnotify.com/api/sms/quick}
  sender:
    id: ${MNOTIFY_SENDER_ID:your-sender-id}
  enabled: ${SMS_ENABLED:false}