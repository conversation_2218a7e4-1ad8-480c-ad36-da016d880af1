package com.juls.sowlstudios.controller;


import com.juls.sowlstudios.dto.BookingDto;
import com.juls.sowlstudios.dto.response.ApiResponse;
import com.juls.sowlstudios.dto.response.BookingResponseDto;
import com.juls.sowlstudios.entity.Booking;
import com.juls.sowlstudios.service.BookingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/v1/bookings")
@RequiredArgsConstructor
@Slf4j
public class BookingController {

    private final BookingService bookingService;

    @PostMapping
    public ResponseEntity<ApiResponse<BookingResponseDto>> createBooking(@Valid @RequestBody BookingDto bookingDto) {
        try {
            BookingResponseDto booking = bookingService.createBooking(bookingDto);
            log.info("New booking created with ID: {}", booking.getId());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Booking created successfully", booking));
        } catch (Exception e) {
            log.error("Error creating booking", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create booking: " + e.getMessage()));
        }
    }

    @GetMapping
    public ResponseEntity<ApiResponse<Page<BookingResponseDto>>> getAllBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Booking.BookingStatus status) {

        Page<BookingResponseDto> bookings = bookingService.getAllBookings(page, size, status);
        return ResponseEntity.ok(ApiResponse.success(bookings));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<BookingResponseDto>> getBookingById(@PathVariable Long id) {
        try {
            BookingResponseDto booking = bookingService.getBookingById(id);
            return ResponseEntity.ok(ApiResponse.success(booking));
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<BookingResponseDto>>> searchBookings(
            @RequestParam(required = false) Booking.BookingStatus status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (startDate != null && !startDate.isEmpty()) {
            startDateTime = LocalDateTime.parse(startDate + "T00:00:00");
        }
        if (endDate != null && !endDate.isEmpty()) {
            endDateTime = LocalDateTime.parse(endDate + "T23:59:59");
        }

        Page<BookingResponseDto> bookings = bookingService.searchBookings(status, startDateTime, endDateTime, page, size);
        return ResponseEntity.ok(ApiResponse.success(bookings));
    }
}
