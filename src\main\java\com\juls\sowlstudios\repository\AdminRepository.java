package com.juls.sowlstudios.repository;



import com.juls.sowlstudios.entity.Admin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface AdminRepository extends JpaRepository<Admin, Long> {

    Optional<Admin> findByUsername(String username);

    Optional<Admin> findByUsernameAndIsActive(String username, Boolean isActive);

    boolean existsByUsername(String username);

    @Modifying
    @Transactional
    @Query("UPDATE Admin a SET a.lastLogin = :loginTime WHERE a.id = :adminId")
    void updateLastLogin(@Param("adminId") Long adminId, @Param("loginTime") LocalDateTime loginTime);
}