package com.juls.sowlstudios.service;


import com.juls.sowlstudios.entity.Booking;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;

    @Value("${app.email.from}")
    private String fromEmail;

    @Value("${app.email.admin}")
    private String adminEmail;

    @Value("${app.email.enabled:true}")
    private boolean emailEnabled;

    @Async("taskExecutor")
    public void sendBookingConfirmation(Booking booking) {
        if (!emailEnabled) {
            log.info("Email service is disabled, skipping email for booking ID: {}", booking.getId());
            return;
        }

        try {
            // Email to admin
            sendAdminNotification(booking);
            log.info("Email notifications sent for booking ID: {}", booking.getId());

        } catch (Exception e) {
            log.error("Failed to send email for booking ID: {}", booking.getId(), e);
        }
    }

    private void sendAdminNotification(Booking booking) {
        SimpleMailMessage adminMessage = new SimpleMailMessage();
        adminMessage.setFrom(fromEmail);
        adminMessage.setTo(adminEmail);
        adminMessage.setSubject("New Photography Booking Received - ID: " + booking.getId());
        adminMessage.setText(buildAdminEmailContent(booking));

        mailSender.send(adminMessage);
        log.info("Admin notification email sent for booking ID: {}", booking.getId());
    }

    @Async("taskExecutor")
    public void sendStatusUpdateNotification(Booking booking, Booking.BookingStatus oldStatus) {
        if (!emailEnabled) {
            return;
        }

        try {
            SimpleMailMessage adminMessage = new SimpleMailMessage();
            adminMessage.setFrom(fromEmail);
            adminMessage.setTo(adminEmail);
            adminMessage.setSubject("Booking Status Updated - ID: " + booking.getId());
            adminMessage.setText(buildStatusUpdateContent(booking, oldStatus));

            mailSender.send(adminMessage);
            log.info("Status update email sent for booking ID: {}", booking.getId());

        } catch (Exception e) {
            log.error("Failed to send status update email for booking ID: {}", booking.getId(), e);
        }
    }

    private String buildAdminEmailContent(Booking booking) {
        return String.format("""
            New photography booking received:
            
            Booking Details:
            - ID: %d
            - Customer: %s %s
            - Phone: %s
            - School/University: %s
            - Graduation Date: %s
            - Package: %s
            - Preferred Location: %s
            - Additional Requests: %s
            - Status: %s
            - Created: %s
            
            Please review and take appropriate action.
            """,
                booking.getId(),
                booking.getFirstName(), booking.getLastName(),
                booking.getPhoneNumber(),
                booking.getSchoolUniversity(),
                booking.getGraduationDate(),
                booking.getPackagePreference(),
                booking.getPreferredLocation() != null ? booking.getPreferredLocation() : "Not specified",
                booking.getAdditionalRequests() != null ? booking.getAdditionalRequests() : "None",
                booking.getStatus(),
                booking.getCreatedAt());
    }

    private String buildStatusUpdateContent(Booking booking, Booking.BookingStatus oldStatus) {
        return String.format("""
            Booking status has been updated:
            
            Booking ID: %d
            Customer: %s %s
            Phone: %s
            Previous Status: %s
            New Status: %s
            Updated: %s
            """,
                booking.getId(),
                booking.getFirstName(), booking.getLastName(),
                booking.getPhoneNumber(),
                oldStatus,
                booking.getStatus(),
                booking.getUpdatedAt());
    }
}